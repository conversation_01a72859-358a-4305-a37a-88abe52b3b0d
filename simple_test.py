#!/usr/bin/env python3
"""
Simple test for the Anthropic proxy server
"""

import json
import httpx
import asyncio

async def test_simple_request():
    """Test a simple message request"""
    
    # Test data
    test_request = {
        "model": "claude-3-haiku-20240307",  # This should map to SMALL_MODEL
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "Hello! Please respond with a simple greeting."
            }
        ]
    }
    
    headers = {
        "Content-Type": "application/json",
        "x-api-key": "test-key",  # Dummy key for testing
        "anthropic-version": "2023-06-01"
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print("🚀 Sending test request to proxy server...")
            print(f"Request: {json.dumps(test_request, indent=2)}")
            
            response = await client.post(
                "http://127.0.0.1:8082/v1/messages",
                json=test_request,
                headers=headers
            )
            
            print(f"\n📊 Response Status: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"✅ Success! Response: {json.dumps(response_data, indent=2)}")
            else:
                print(f"❌ Error: {response.text}")
                
    except Exception as e:
        print(f"❌ Exception occurred: {e}")

async def test_health_check():
    """Test the health check endpoint"""
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            print("🔍 Testing health check endpoint...")
            response = await client.get("http://127.0.0.1:8082/")
            print(f"Health check response: {response.text}")
            print(f"Status: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check failed: {e}")

async def test_model_mapping():
    """Test model mapping functionality"""
    
    test_cases = [
        {
            "name": "Haiku model (should map to SMALL_MODEL)",
            "model": "claude-3-haiku-20240307"
        },
        {
            "name": "Sonnet model (should map to BIG_MODEL)", 
            "model": "claude-3-sonnet-20240229"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 Testing {test_case['name']}")
        
        test_request = {
            "model": test_case["model"],
            "max_tokens": 50,
            "messages": [
                {
                    "role": "user",
                    "content": "Say hello in one word."
                }
            ]
        }
        
        headers = {
            "Content-Type": "application/json",
            "x-api-key": "test-key",
            "anthropic-version": "2023-06-01"
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    "http://127.0.0.1:8082/v1/messages",
                    json=test_request,
                    headers=headers
                )
                
                print(f"Status: {response.status_code}")
                if response.status_code != 200:
                    print(f"Error: {response.text}")
                else:
                    response_data = response.json()
                    print(f"Model used: {response_data.get('model', 'unknown')}")
                    if 'content' in response_data and response_data['content']:
                        content = response_data['content'][0].get('text', '')[:100]
                        print(f"Response preview: {content}...")
                        
        except Exception as e:
            print(f"❌ Test failed: {e}")

async def main():
    print("🔧 Starting Anthropic Proxy Tests")
    print("=" * 50)
    
    await test_health_check()
    print("\n" + "=" * 50)
    
    await test_model_mapping()
    print("\n" + "=" * 50)
    
    print("✅ Tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
