#!/usr/bin/env python3
"""
Test the fix for "Short substrate on input" error
"""

import json
import httpx
import asyncio

async def test_substrate_error_fix():
    """Test that the substrate error is now handled gracefully"""
    
    test_cases = [
        {
            "name": "Normal message",
            "request": {
                "model": "claude-3-sonnet-20240229",
                "max_tokens": 50,
                "messages": [{"role": "user", "content": "Hello"}]
            }
        },
        {
            "name": "Empty content",
            "request": {
                "model": "claude-3-sonnet-20240229", 
                "max_tokens": 50,
                "messages": [{"role": "user", "content": ""}]
            }
        },
        {
            "name": "Whitespace only",
            "request": {
                "model": "claude-3-sonnet-20240229",
                "max_tokens": 50, 
                "messages": [{"role": "user", "content": "   "}]
            }
        },
        {
            "name": "Special characters",
            "request": {
                "model": "claude-3-sonnet-20240229",
                "max_tokens": 50,
                "messages": [{"role": "user", "content": "Hello! 🌟 How are you? 中文测试"}]
            }
        },
        {
            "name": "Very short message",
            "request": {
                "model": "claude-3-sonnet-20240229",
                "max_tokens": 50,
                "messages": [{"role": "user", "content": "Hi"}]
            }
        }
    ]
    
    headers = {
        "Content-Type": "application/json",
        "x-api-key": "test-key",
        "anthropic-version": "2023-06-01"
    }
    
    for test_case in test_cases:
        print(f"\n🧪 Testing: {test_case['name']}")
        print(f"   Content: '{test_case['request']['messages'][0]['content']}'")
        
        try:
            async with httpx.AsyncClient(timeout=20.0) as client:
                response = await client.post(
                    "http://127.0.0.1:8082/v1/messages",
                    json=test_case['request'],
                    headers=headers
                )
                
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    response_data = response.json()
                    content = response_data.get('content', [])
                    
                    if content and len(content) > 0:
                        text = content[0].get('text', '')
                        
                        if 'Input Error' in text:
                            print("   ✅ Input error handled gracefully!")
                            print(f"   📋 Error explanation provided")
                        elif 'Authentication Required' in text:
                            print("   ✅ Auth error handled gracefully!")
                        elif 'Format Error' in text:
                            print("   ✅ Format error handled gracefully!")
                        else:
                            print("   ✅ Normal response received")
                            print(f"   Response preview: {text[:100]}...")
                    else:
                        print("   ⚠️ Empty response content")
                        
                elif response.status_code == 500:
                    error_data = response.json()
                    error_detail = error_data.get("detail", "")
                    
                    if "Short substrate" in error_detail:
                        print("   ❌ Substrate error still occurring")
                        print(f"   Error: {error_detail}")
                    else:
                        print("   ✅ Different error (expected)")
                        print(f"   Error: {error_detail[:100]}...")
                else:
                    print(f"   ❓ Unexpected status: {response.status_code}")
                    print(f"   Response: {response.text[:100]}...")
                    
        except Exception as e:
            print(f"   ❌ Exception: {e}")

async def main():
    print("🔧 Testing Substrate Error Fix")
    print("=" * 60)
    
    await test_substrate_error_fix()
    
    print("\n" + "=" * 60)
    print("✅ Substrate error fix test completed!")
    print("\n💡 Expected results:")
    print("   - No more 'Short substrate on input' errors")
    print("   - Helpful error messages for input issues")
    print("   - Graceful handling of edge cases")

if __name__ == "__main__":
    asyncio.run(main())
