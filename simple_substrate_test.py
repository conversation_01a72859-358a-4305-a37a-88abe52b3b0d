import httpx
import json
import asyncio

async def test():
    try:
        async with httpx.AsyncClient(timeout=15.0) as client:
            response = await client.post(
                'http://127.0.0.1:8082/v1/messages',
                json={'model': 'claude-3-sonnet-20240229', 'max_tokens': 50, 'messages': [{'role': 'user', 'content': 'Hello'}]},
                headers={'Content-Type': 'application/json', 'x-api-key': 'test', 'anthropic-version': '2023-06-01'}
            )
            print(f'Status: {response.status_code}')
            if response.status_code == 200:
                data = response.json()
                content = data.get('content', [])
                if content:
                    text = content[0].get('text', '')
                    if 'Input Error' in text:
                        print('✅ Input error handled gracefully!')
                    elif 'Authentication Required' in text:
                        print('✅ Auth error handled gracefully!')
                    else:
                        print('✅ Normal response')
                        print(f'Preview: {text[:100]}...')
            else:
                error_data = response.json()
                error_detail = error_data.get('detail', '')
                if 'Short substrate' in error_detail:
                    print('❌ Substrate error still occurring')
                else:
                    print('✅ Different error (expected)')
                print(f'Error: {error_detail[:200]}...')
    except Exception as e:
        print(f'❌ Exception: {e}')

asyncio.run(test())
