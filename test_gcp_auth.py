#!/usr/bin/env python3
"""
Test Google Cloud Platform authentication
"""

import os
import sys

def test_gcp_auth():
    """Test if GCP authentication is working"""
    
    print("🔍 Testing Google Cloud Platform Authentication")
    print("=" * 60)
    
    # Check environment variables
    gcp_creds = os.environ.get("GOOGLE_APPLICATION_CREDENTIALS")
    if gcp_creds:
        print(f"✅ GOOGLE_APPLICATION_CREDENTIALS set: {gcp_creds}")
        if os.path.exists(gcp_creds):
            print(f"✅ Credentials file exists")
        else:
            print(f"❌ Credentials file not found: {gcp_creds}")
            return False
    else:
        print("⚠️ GOOGLE_APPLICATION_CREDENTIALS not set, trying default credentials...")
    
    # Test Google Auth
    try:
        import google.auth
        from google.auth import default
        
        print("\n🔐 Testing Google Auth...")
        credentials, project = default()
        print(f"✅ Default credentials found!")
        print(f"   Project: {project}")
        print(f"   Credentials type: {type(credentials).__name__}")
        
        # Test if credentials are valid
        if hasattr(credentials, 'valid'):
            if credentials.valid:
                print("✅ Credentials are valid")
            else:
                print("⚠️ Credentials may need refresh")
                try:
                    credentials.refresh(google.auth.transport.requests.Request())
                    print("✅ Credentials refreshed successfully")
                except Exception as e:
                    print(f"❌ Failed to refresh credentials: {e}")
                    return False
        
    except Exception as e:
        print(f"❌ Google Auth failed: {e}")
        print("\n💡 Solutions:")
        print("   1. Run: gcloud auth application-default login")
        print("   2. Or set GOOGLE_APPLICATION_CREDENTIALS to a service account key file")
        return False
    
    # Test Vertex AI initialization
    try:
        print("\n🔵 Testing Vertex AI initialization...")
        import vertexai
        
        project_id = project or "timeline-464108"
        vertexai.init(project=project_id, location="global")
        print(f"✅ Vertex AI initialized successfully!")
        print(f"   Project: {project_id}")
        print(f"   Location: global")
        
    except Exception as e:
        print(f"❌ Vertex AI initialization failed: {e}")
        return False
    
    # Test model access
    try:
        print("\n🤖 Testing model access...")
        from vertexai.generative_models import GenerativeModel
        
        model = GenerativeModel("gemini-1.5-flash")
        print("✅ Model initialized successfully!")
        
        # Try a simple generation (this might fail due to quota/permissions, but initialization should work)
        print("   Note: Actual model calls may require additional permissions")
        
    except Exception as e:
        print(f"⚠️ Model initialization warning: {e}")
        print("   This might be due to permissions or quota limits")
    
    print("\n" + "=" * 60)
    print("✅ GCP Authentication test completed!")
    return True

def show_setup_instructions():
    """Show setup instructions"""
    
    print("\n📋 Setup Instructions:")
    print("=" * 60)
    
    print("\n🔧 Option 1: Application Default Credentials (Recommended)")
    print("   gcloud auth application-default login")
    print("   gcloud config set project timeline-464108")
    
    print("\n🔧 Option 2: Service Account Key File")
    print("   1. Download service account key from Google Cloud Console")
    print("   2. export GOOGLE_APPLICATION_CREDENTIALS='/path/to/key.json'")
    print("   3. Add to .env file: GOOGLE_APPLICATION_CREDENTIALS='/path/to/key.json'")
    
    print("\n🔧 Option 3: Create New Service Account")
    print("   gcloud iam service-accounts create vertex-ai-proxy")
    print("   gcloud projects add-iam-policy-binding timeline-464108 \\")
    print("       --member='serviceAccount:<EMAIL>' \\")
    print("       --role='roles/aiplatform.user'")
    print("   gcloud iam service-accounts keys create key.json \\")
    print("       --iam-account=<EMAIL>")
    print("   export GOOGLE_APPLICATION_CREDENTIALS='./key.json'")

if __name__ == "__main__":
    success = test_gcp_auth()
    
    if not success:
        show_setup_instructions()
        sys.exit(1)
    else:
        print("\n🎉 All tests passed! Your GCP authentication is working correctly.")
        print("   You can now use Vertex AI with the proxy server.")
