import httpx
import json
import asyncio

async def test():
    async with httpx.AsyncClient(timeout=15.0) as client:
        response = await client.post(
            'http://127.0.0.1:8082/v1/messages',
            json={'model': 'claude-3-sonnet-20240229', 'max_tokens': 50, 'messages': [{'role': 'user', 'content': 'Hello'}]},
            headers={'Content-Type': 'application/json', 'x-api-key': 'test', 'anthropic-version': '2023-06-01'}
        )
        print(f'Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            content = data.get('content', [])
            if content:
                text = content[0].get('text', '')
                if 'Authentication Required' in text:
                    print('✅ Auth error handled gracefully!')
                    print('First 200 chars:', text[:200])
                else:
                    print('✅ Normal response')
                    print('Response:', json.dumps(data, indent=2)[:300])
        else:
            print(f'Error: {response.text}')

asyncio.run(test())
