#!/usr/bin/env python3
"""
Setup mock GCP authentication for testing purposes
"""

import os
import json
import tempfile
from pathlib import Path

def create_mock_service_account_key():
    """Create a mock service account key for testing"""
    
    mock_key = *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    
    # Create temporary file
    temp_dir = Path("/tmp")
    key_file = temp_dir / "mock_gcp_service_account.json"
    
    with open(key_file, 'w') as f:
        json.dump(mock_key, f, indent=2)
    
    print(f"✅ Created mock service account key: {key_file}")
    return str(key_file)

def setup_environment():
    """Setup environment variables for mock authentication"""
    
    key_file = create_mock_service_account_key()
    
    # Set environment variable
    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = key_file
    
    # Also add to .env file
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r') as f:
            content = f.read()
        
        # Remove existing GOOGLE_APPLICATION_CREDENTIALS line
        lines = content.split('\n')
        lines = [line for line in lines if not line.startswith('GOOGLE_APPLICATION_CREDENTIALS=')]
        
        # Add new line
        lines.append(f'GOOGLE_APPLICATION_CREDENTIALS="{key_file}"')
        
        with open(env_file, 'w') as f:
            f.write('\n'.join(lines))
    
    print(f"✅ Set GOOGLE_APPLICATION_CREDENTIALS={key_file}")
    print(f"✅ Updated .env file")
    
    return key_file

def create_fallback_solution():
    """Create a fallback solution that doesn't require real GCP auth"""
    
    print("🔧 Creating fallback solution for Vertex AI...")
    
    # Create a modified version of the adapter that handles auth errors gracefully
    fallback_code = '''
# Add this to vertex_ai_adapter.py to handle auth errors gracefully

def _handle_auth_error(self, error_msg: str) -> Dict[str, Any]:
    """Handle authentication errors gracefully"""
    
    # Return a mock response for testing
    mock_response = {
        "id": f"mock-response-{hash(error_msg) % 1000000}",
        "object": "chat.completion",
        "created": int(time.time()),
        "model": "gemini-1.5-flash",
        "choices": [{
            "index": 0,
            "message": {
                "role": "assistant",
                "content": f"⚠️ Vertex AI authentication not configured. Error: {error_msg}\\n\\nTo fix this, please set up Google Cloud authentication:\\n1. Run: gcloud auth application-default login\\n2. Or set GOOGLE_APPLICATION_CREDENTIALS to a service account key file",
            },
            "finish_reason": "stop"
        }],
        "usage": {
            "prompt_tokens": 0,
            "completion_tokens": 50,
            "total_tokens": 50
        }
    }
    
    return mock_response
'''
    
    print("💡 Fallback solution created!")
    print("   This will return helpful error messages instead of crashing")
    
    return fallback_code

def main():
    print("🔧 Setting up Mock GCP Authentication")
    print("=" * 60)
    
    try:
        key_file = setup_environment()
        
        print("\n✅ Mock authentication setup completed!")
        print(f"   Key file: {key_file}")
        print(f"   Environment variable set: GOOGLE_APPLICATION_CREDENTIALS")
        
        print("\n⚠️ Important Notes:")
        print("   - This is a MOCK authentication for testing only")
        print("   - Real Vertex AI calls will still fail without proper credentials")
        print("   - For production, use real GCP authentication")
        
        print("\n🔧 To set up real authentication:")
        print("   1. gcloud auth application-default login")
        print("   2. gcloud config set project timeline-464108")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        
        print("\n🔄 Creating fallback solution...")
        fallback_code = create_fallback_solution()
        print(fallback_code)

if __name__ == "__main__":
    main()
