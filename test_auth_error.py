#!/usr/bin/env python3
"""
Test the improved authentication error handling
"""

import json
import httpx
import asyncio

async def test_auth_error_handling():
    """Test that authentication errors are handled gracefully"""
    
    test_request = {
        "model": "claude-3-sonnet-20240229",
        "max_tokens": 50,
        "messages": [
            {
                "role": "user",
                "content": "Hello! This should trigger an auth error but handle it gracefully."
            }
        ]
    }
    
    headers = {
        "Content-Type": "application/json",
        "x-api-key": "test-key",
        "anthropic-version": "2023-06-01"
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print("🔐 Testing improved authentication error handling...")
            
            response = await client.post(
                "http://127.0.0.1:8082/v1/messages",
                json=test_request,
                headers=headers
            )
            
            print(f"📊 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print("✅ Request succeeded!")
                
                # Check if it's an auth error response
                content = response_data.get('content', [])
                if content and len(content) > 0:
                    text = content[0].get('text', '')
                    if 'Authentication Required' in text:
                        print("✅ Authentication error handled gracefully!")
                        print("📋 Helpful instructions provided to user")
                        print(f"Response preview: {text[:200]}...")
                    else:
                        print("✅ Normal response received")
                        print(f"Response: {json.dumps(response_data, indent=2)}")
                        
            else:
                print(f"❌ Unexpected status code: {response.status_code}")
                print(f"Response: {response.text}")
                
    except Exception as e:
        print(f"❌ Exception occurred: {e}")

async def main():
    print("🔧 Testing Improved Authentication Error Handling")
    print("=" * 60)
    
    await test_auth_error_handling()
    
    print("\n" + "=" * 60)
    print("✅ Test completed!")

if __name__ == "__main__":
    asyncio.run(main())
