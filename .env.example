# This file provides an example of the environment variables used by the proxy.
# Copy this file to '.env' and fill in the values for your setup.

# --- Tier 1: Mapped Model Configurations ---
# This is the primary and most flexible way to configure the proxy.
# It allows you to define independent deployments for common model types like "sonnet" (big) and "haiku" (small).
# You can point them to different providers, each with its own API key and base URL.

# -- Settings for "big" models (requests containing 'sonnet') --
# Provider for the big model. Supported: 'openai', 'gemini', 'vertex', 'xai', 'anthropic'.
BIG_MODEL_PROVIDER="openai"
# The actual model name to be called by the provider.
BIG_MODEL_NAME="gpt-4.1"
# API key for the big model's provider. If not set, the Tier 2 global key will be used as a fallback.
BIG_MODEL_API_KEY=""
# Optional: Custom base URL for the big model's provider (e.g., for local LLMs or third-party services).
BIG_MODEL_API_BASE=""


# -- Settings for "small" models (requests containing 'haiku') --
# Provider for the small model.
SMALL_MODEL_PROVIDER="openai"
# The actual model name to be called by the provider.
SMALL_MODEL_NAME="gpt-4.1-mini"
# API key for the small model's provider.
SMALL_MODEL_API_KEY=""
# Optional: Custom base URL for the small model's provider.
SMALL_MODEL_API_BASE=""


# --- Tier 2: Global Provider Configurations ---
# These variables act as fallbacks for Tier 1 and as the primary configuration
# for direct, provider-prefixed requests (e.g., "openai/my-custom-model").

# -- Global Keys --
ANTHROPIC_API_KEY=""
OPENAI_API_KEY=""
GEMINI_API_KEY=""
XAI_API_KEY=""

# -- Global Base URLs (Optional) --
# Use these to redirect all calls for a specific provider to a custom endpoint.
ANTHROPIC_API_BASE=""
OPENAI_API_BASE=""
GEMINI_API_BASE=""
XAI_API_BASE=""


# --- Vertex AI Specific Configuration (Global) ---
# Vertex AI uses a Project ID and Location instead of a single Base URL.
# These are required if you intend to use 'vertex' as a provider in any tier.
VERTEX_PROJECT_ID=""
VERTEX_LOCATION="us-central1"
# Optional: Path to a service account JSON file for authentication.
# If not set, Application Default Credentials (ADC) will be used.
# GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/credentials.json"


# --- Logging Configuration ---
# -- Log Verbosity --
# Control the log level for the file and the console output.
# Supported levels: DEBUG, INFO, WARNING, ERROR, CRITICAL
FILE_LOG_LEVEL="DEBUG"
CONSOLE_LOG_LEVEL="INFO"

# -- Log Content --
# Set to "true" to enable detailed logging of the full request and response bodies.
# This is useful for debugging but can be verbose in production.
LOG_REQUEST_BODY="false"
LOG_RESPONSE_BODY="false"