#!/usr/bin/env python3
"""
Advanced test for the Anthropic proxy server including tool calls
"""

import json
import httpx
import asyncio

async def test_tool_calling():
    """Test tool calling functionality"""
    
    # Test data with tool definition
    test_request = {
        "model": "claude-3-sonnet-20240229",
        "max_tokens": 200,
        "messages": [
            {
                "role": "user",
                "content": "What's the weather like in New York? Use the weather tool to check."
            }
        ],
        "tools": [
            {
                "name": "get_weather",
                "description": "Get the current weather for a location",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The city and state, e.g. San Francisco, CA"
                        },
                        "unit": {
                            "type": "string",
                            "enum": ["celsius", "fahrenheit"],
                            "description": "The unit of temperature"
                        }
                    },
                    "required": ["location"]
                }
            }
        ],
        "tool_choice": {"type": "auto"}
    }
    
    headers = {
        "Content-Type": "application/json",
        "x-api-key": "test-key",
        "anthropic-version": "2023-06-01"
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print("🛠️ Testing tool calling functionality...")
            
            response = await client.post(
                "http://127.0.0.1:8082/v1/messages",
                json=test_request,
                headers=headers
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"✅ Tool calling test successful!")
                print(f"Model: {response_data.get('model')}")
                print(f"Stop reason: {response_data.get('stop_reason')}")
                
                # Check if tool was called
                content = response_data.get('content', [])
                for block in content:
                    if block.get('type') == 'tool_use':
                        print(f"🔧 Tool called: {block.get('name')}")
                        print(f"Tool input: {json.dumps(block.get('input', {}), indent=2)}")
                    elif block.get('type') == 'text':
                        print(f"Text response: {block.get('text', '')[:100]}...")
                        
            else:
                print(f"❌ Tool calling test failed: {response.text}")
                
    except Exception as e:
        print(f"❌ Tool calling test exception: {e}")

async def test_streaming():
    """Test streaming functionality"""
    
    test_request = {
        "model": "claude-3-haiku-20240307",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "Count from 1 to 10 slowly, with a brief explanation for each number."
            }
        ],
        "stream": True
    }
    
    headers = {
        "Content-Type": "application/json",
        "x-api-key": "test-key",
        "anthropic-version": "2023-06-01"
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print("🌊 Testing streaming functionality...")
            
            async with client.stream(
                "POST",
                "http://127.0.0.1:8082/v1/messages",
                json=test_request,
                headers=headers
            ) as response:
                
                print(f"Streaming status: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ Streaming response received:")
                    chunk_count = 0
                    async for chunk in response.aiter_text():
                        if chunk.strip():
                            chunk_count += 1
                            if chunk_count <= 5:  # Show first 5 chunks
                                print(f"Chunk {chunk_count}: {chunk[:100]}...")
                            elif chunk_count == 6:
                                print("... (more chunks received)")
                    
                    print(f"Total chunks received: {chunk_count}")
                else:
                    content = await response.aread()
                    print(f"❌ Streaming test failed: {content.decode()}")
                    
    except Exception as e:
        print(f"❌ Streaming test exception: {e}")

async def test_multi_turn_conversation():
    """Test multi-turn conversation"""
    
    test_request = {
        "model": "claude-3-haiku-20240307",
        "max_tokens": 150,
        "messages": [
            {
                "role": "user",
                "content": "Hello! My name is Alice."
            },
            {
                "role": "assistant",
                "content": "Hello Alice! It's nice to meet you. How are you doing today?"
            },
            {
                "role": "user",
                "content": "I'm doing well, thank you! Can you remember my name?"
            }
        ]
    }
    
    headers = {
        "Content-Type": "application/json",
        "x-api-key": "test-key",
        "anthropic-version": "2023-06-01"
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print("💬 Testing multi-turn conversation...")
            
            response = await client.post(
                "http://127.0.0.1:8082/v1/messages",
                json=test_request,
                headers=headers
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"✅ Multi-turn conversation test successful!")
                
                content = response_data.get('content', [])
                for block in content:
                    if block.get('type') == 'text':
                        text = block.get('text', '')
                        print(f"Response: {text}")
                        # Check if the model remembered the name
                        if 'Alice' in text:
                            print("🎉 Model correctly remembered the name!")
                        
            else:
                print(f"❌ Multi-turn conversation test failed: {response.text}")
                
    except Exception as e:
        print(f"❌ Multi-turn conversation test exception: {e}")

async def main():
    print("🚀 Starting Advanced Anthropic Proxy Tests")
    print("=" * 60)
    
    await test_tool_calling()
    print("\n" + "=" * 60)
    
    await test_streaming()
    print("\n" + "=" * 60)
    
    await test_multi_turn_conversation()
    print("\n" + "=" * 60)
    
    print("✅ All advanced tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
