#!/usr/bin/env python3
"""
Vertex AI specific test for the Anthropic proxy server
"""

import json
import httpx
import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_vertex_ai_simple():
    """Test a simple Vertex AI request"""

    test_request = {
        "model": "claude-3-sonnet-20240229",  # This should map to Vertex AI
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "Hello! Please tell me about Google Cloud Platform in one sentence."
            }
        ]
    }

    headers = {
        "Content-Type": "application/json",
        "x-api-key": "test-key",
        "anthropic-version": "2023-06-01"
    }

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print("🔵 Testing Vertex AI integration...")
            print(f"Request: {json.dumps(test_request, indent=2)}")

            response = await client.post(
                "http://127.0.0.1:8082/v1/messages",
                json=test_request,
                headers=headers
            )

            print(f"\n📊 Response Status: {response.status_code}")

            if response.status_code == 200:
                response_data = response.json()
                print(f"✅ Success! Response: {json.dumps(response_data, indent=2)}")
            else:
                print(f"❌ Error: {response.text}")

    except Exception as e:
        print(f"❌ Exception occurred: {e}")

async def test_vertex_ai_streaming():
    """Test Vertex AI streaming"""

    test_request = {
        "model": "claude-3-haiku-20240307",  # This should map to Vertex AI
        "max_tokens": 150,
        "stream": True,
        "messages": [
            {
                "role": "user",
                "content": "Count from 1 to 5 and explain each number briefly."
            }
        ]
    }

    headers = {
        "Content-Type": "application/json",
        "x-api-key": "test-key",
        "anthropic-version": "2023-06-01"
    }

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print("🌊 Testing Vertex AI streaming...")

            async with client.stream(
                "POST",
                "http://127.0.0.1:8082/v1/messages",
                json=test_request,
                headers=headers
            ) as response:

                print(f"Streaming status: {response.status_code}")

                if response.status_code == 200:
                    print("✅ Streaming response received:")
                    chunk_count = 0
                    async for chunk in response.aiter_text():
                        if chunk.strip():
                            chunk_count += 1
                            if chunk_count <= 5:
                                print(f"Chunk {chunk_count}: {chunk[:100]}...")
                            elif chunk_count == 6:
                                print("... (more chunks received)")

                    print(f"Total chunks received: {chunk_count}")
                else:
                    content = await response.aread()
                    print(f"❌ Streaming test failed: {content.decode()}")

    except Exception as e:
        print(f"❌ Streaming test exception: {e}")

async def test_vertex_ai_tools():
    """Test Vertex AI with tools"""

    test_request = {
        "model": "claude-3-sonnet-20240229",
        "max_tokens": 200,
        "messages": [
            {
                "role": "user",
                "content": "What's the weather like in San Francisco? Use the weather tool."
            }
        ],
        "tools": [
            {
                "name": "get_weather",
                "description": "Get current weather for a location",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "City name"
                        },
                        "unit": {
                            "type": "string",
                            "enum": ["celsius", "fahrenheit"],
                            "description": "Temperature unit"
                        }
                    },
                    "required": ["location"]
                }
            }
        ],
        "tool_choice": {"type": "auto"}
    }

    headers = {
        "Content-Type": "application/json",
        "x-api-key": "test-key",
        "anthropic-version": "2023-06-01"
    }

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print("🛠️ Testing Vertex AI with tools...")

            response = await client.post(
                "http://127.0.0.1:8082/v1/messages",
                json=test_request,
                headers=headers
            )

            print(f"Status: {response.status_code}")

            if response.status_code == 200:
                response_data = response.json()
                print(f"✅ Tool test successful!")
                print(f"Model: {response_data.get('model')}")
                print(f"Stop reason: {response_data.get('stop_reason')}")

                content = response_data.get('content', [])
                for block in content:
                    if block.get('type') == 'tool_use':
                        print(f"🔧 Tool called: {block.get('name')}")
                        print(f"Tool input: {json.dumps(block.get('input', {}), indent=2)}")
                    elif block.get('type') == 'text':
                        print(f"Text response: {block.get('text', '')[:100]}...")

            else:
                print(f"❌ Tool test failed: {response.text}")

    except Exception as e:
        print(f"❌ Tool test exception: {e}")

async def main():
    print("🔵 Starting Vertex AI Specific Tests")
    print("=" * 60)

    # Check if Vertex AI is configured
    vertex_project = os.environ.get("VERTEX_PROJECT_ID")
    if not vertex_project:
        print("⚠️ VERTEX_PROJECT_ID not set. Please configure Vertex AI in .env file.")
        return

    print(f"Using Vertex AI Project: {vertex_project}")
    print("=" * 60)

    await test_vertex_ai_simple()
    print("\n" + "=" * 60)

    await test_vertex_ai_streaming()
    print("\n" + "=" * 60)

    await test_vertex_ai_tools()
    print("\n" + "=" * 60)

    print("✅ Vertex AI tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
