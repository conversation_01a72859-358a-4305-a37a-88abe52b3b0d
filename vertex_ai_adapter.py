"""
Vertex AI Adapter for LiteLLM Compatibility

This module provides a compatibility layer for Vertex AI integration with LiteLLM,
addressing async/sync issues and providing fallback mechanisms.
"""

import asyncio
import logging
import json
import os
from typing import Dict, Any, Optional, Union, List
from concurrent.futures import Thread<PERSON>oolExecutor
import functools

# Import Google Cloud libraries with error handling
try:
    import vertexai
    from vertexai.generative_models import GenerativeModel, Part, Content
    from vertexai.generative_models import ChatSession
    from google.cloud import aiplatform
    VERTEX_AI_AVAILABLE = True
except ImportError as e:
    VERTEX_AI_AVAILABLE = False
    logging.warning(f"Vertex AI libraries not available: {e}")

logger = logging.getLogger(__name__)

class VertexAIAdapter:
    """
    Adapter class to handle Vertex AI requests with proper async support
    """

    def __init__(self, project_id: str, location: str = "us-central1"):
        self.project_id = project_id
        self.location = location
        self.executor = ThreadPoolExecutor(max_workers=4)

        if VERTEX_AI_AVAILABLE:
            try:
                # Initialize Vertex AI
                vertexai.init(project=project_id, location=location)
                logger.info(f"✅ Vertex AI initialized: project={project_id}, location={location}")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Vertex AI: {e}")
                raise
        else:
            raise ImportError("Vertex AI libraries are not available. Please install google-cloud-aiplatform.")

    def _sync_chat_completion(self, model_name: str, messages: List[Dict], **kwargs) -> Dict[str, Any]:
        """
        Synchronous chat completion for Vertex AI
        """
        try:
            logger.debug(f"🔵 Starting Vertex AI completion for model: {model_name}")
            logger.debug(f"🔵 Input messages: {messages}")

            # Initialize the model
            model = GenerativeModel(model_name)
            logger.debug(f"🔵 Model initialized: {model_name}")

            # Convert messages to Vertex AI format
            vertex_messages = self._convert_messages_to_vertex_format(messages)
            logger.debug(f"🔵 Converted messages: {vertex_messages}")

            # Extract parameters
            max_output_tokens = kwargs.get('max_output_tokens', kwargs.get('max_tokens', 1024))
            temperature = kwargs.get('temperature', 1.0)
            top_p = kwargs.get('top_p', None)
            top_k = kwargs.get('top_k', None)

            # Build generation config
            generation_config = {
                "max_output_tokens": max_output_tokens,
                "temperature": temperature,
            }
            if top_p is not None:
                generation_config["top_p"] = top_p
            if top_k is not None:
                generation_config["top_k"] = top_k

            # Handle tools if present
            tools = kwargs.get('tools', [])
            vertex_tools = self._convert_tools_to_vertex_format(tools) if tools else None

            # Generate response
            if len(vertex_messages) == 1:
                # Single message
                response = model.generate_content(
                    vertex_messages[0],
                    generation_config=generation_config,
                    tools=vertex_tools
                )
            else:
                # Multi-turn conversation
                chat = model.start_chat()
                for msg in vertex_messages[:-1]:
                    chat.send_message(msg)
                response = chat.send_message(
                    vertex_messages[-1],
                    generation_config=generation_config,
                    tools=vertex_tools
                )

            # Convert response to OpenAI format
            return self._convert_vertex_response_to_openai_format(response, model_name)

        except Exception as e:
            error_str = str(e)
            logger.error(f"❌ Vertex AI sync completion error: {error_str}")
            logger.error(f"❌ Error type: {type(e).__name__}")

            # Handle authentication errors gracefully
            if "credentials" in error_str.lower() or "authentication" in error_str.lower():
                return self._create_auth_error_response(error_str, model_name)

            # Handle specific Vertex AI errors
            if "short substrate" in error_str.lower():
                return self._create_input_error_response(error_str, model_name, messages)

            # Handle other common errors
            if any(keyword in error_str.lower() for keyword in ["invalid", "malformed", "format", "parse"]):
                return self._create_format_error_response(error_str, model_name, messages)

            raise

    async def async_chat_completion(self, model_name: str, messages: List[Dict], **kwargs) -> Dict[str, Any]:
        """
        Asynchronous chat completion for Vertex AI
        """
        loop = asyncio.get_event_loop()

        # Run the sync function in a thread pool
        result = await loop.run_in_executor(
            self.executor,
            functools.partial(self._sync_chat_completion, model_name, messages, **kwargs)
        )

        return result

    def _convert_messages_to_vertex_format(self, messages: List[Dict]) -> List[str]:
        """
        Convert OpenAI format messages to Vertex AI format
        """
        vertex_messages = []

        for message in messages:
            role = message.get('role', 'user')
            content = message.get('content', '')

            # Ensure content is not empty
            if not content:
                content = "Hello"  # Default content if empty

            if isinstance(content, str):
                # Clean and validate the content
                cleaned_content = content.strip()
                if not cleaned_content:
                    cleaned_content = "Hello"
                vertex_messages.append(cleaned_content)
            elif isinstance(content, list):
                # Handle multimodal content
                text_parts = []
                for part in content:
                    if part.get('type') == 'text':
                        text = part.get('text', '').strip()
                        if text:
                            text_parts.append(text)
                    elif part.get('type') == 'image_url':
                        # For now, just add a placeholder for images
                        text_parts.append('[Image content]')

                combined_text = ' '.join(text_parts).strip()
                if not combined_text:
                    combined_text = "Hello"
                vertex_messages.append(combined_text)
            else:
                # Convert to string and clean
                str_content = str(content).strip()
                if not str_content:
                    str_content = "Hello"
                vertex_messages.append(str_content)

        # Ensure we have at least one message
        if not vertex_messages:
            vertex_messages = ["Hello"]

        return vertex_messages

    def _convert_tools_to_vertex_format(self, tools: List[Dict]) -> List:
        """
        Convert OpenAI format tools to Vertex AI format
        """
        if not tools:
            return None

        from vertexai.generative_models import Tool, FunctionDeclaration

        vertex_tools = []

        for tool in tools:
            if tool.get('type') == 'function':
                function = tool.get('function', {})

                # Create function declaration
                func_decl = FunctionDeclaration(
                    name=function.get('name', ''),
                    description=function.get('description', ''),
                    parameters=function.get('parameters', {})
                )

                # Create tool with function declaration
                vertex_tool = Tool(function_declarations=[func_decl])
                vertex_tools.append(vertex_tool)

        return vertex_tools if vertex_tools else None

    def _convert_vertex_response_to_openai_format(self, response, model_name: str) -> Dict[str, Any]:
        """
        Convert Vertex AI response to OpenAI format
        """
        try:
            # Extract text content
            text_content = ""
            if hasattr(response, 'text') and response.text:
                text_content = response.text
            elif hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, 'content') and candidate.content:
                    for part in candidate.content.parts:
                        if hasattr(part, 'text'):
                            text_content += part.text

            # Extract function calls if present
            tool_calls = []
            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, 'content') and candidate.content:
                    for part in candidate.content.parts:
                        if hasattr(part, 'function_call'):
                            tool_calls.append({
                                "id": f"call_{len(tool_calls)}",
                                "type": "function",
                                "function": {
                                    "name": part.function_call.name,
                                    "arguments": json.dumps(dict(part.function_call.args))
                                }
                            })

            # Build OpenAI-compatible response
            openai_response = {
                "id": f"chatcmpl-vertex-{hash(text_content) % 1000000}",
                "object": "chat.completion",
                "created": int(asyncio.get_event_loop().time()),
                "model": model_name,
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": text_content,
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": 0,  # Vertex AI doesn't provide token counts
                    "completion_tokens": 0,
                    "total_tokens": 0
                }
            }

            # Add tool calls if present
            if tool_calls:
                openai_response["choices"][0]["message"]["tool_calls"] = tool_calls
                openai_response["choices"][0]["finish_reason"] = "tool_calls"

            return openai_response

        except Exception as e:
            logger.error(f"❌ Error converting Vertex AI response: {e}")
            # Return a basic error response
            return {
                "id": "chatcmpl-vertex-error",
                "object": "chat.completion",
                "created": int(asyncio.get_event_loop().time()),
                "model": model_name,
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": f"Error processing response: {str(e)}",
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": 0,
                    "completion_tokens": 0,
                    "total_tokens": 0
                }
            }

    def _create_auth_error_response(self, error_msg: str, model_name: str) -> Dict[str, Any]:
        """
        Create a helpful error response for authentication issues
        """
        import time

        auth_help_message = f"""🔐 Vertex AI Authentication Required

Error: {error_msg}

To fix this issue, please set up Google Cloud authentication:

📋 Option 1: Application Default Credentials (Recommended)
   gcloud auth application-default login
   gcloud config set project timeline-464108

📋 Option 2: Service Account Key File
   1. Download service account key from Google Cloud Console
   2. export GOOGLE_APPLICATION_CREDENTIALS='/path/to/key.json'
   3. Add to .env file: GOOGLE_APPLICATION_CREDENTIALS='/path/to/key.json'

📋 Option 3: Create New Service Account
   gcloud iam service-accounts create vertex-ai-proxy
   gcloud projects add-iam-policy-binding timeline-464108 \\
       --member='serviceAccount:<EMAIL>' \\
       --role='roles/aiplatform.user'
   gcloud iam service-accounts keys create key.json \\
       --iam-account=<EMAIL>
   export GOOGLE_APPLICATION_CREDENTIALS='./key.json'

For more information, visit: https://cloud.google.com/docs/authentication/getting-started
"""

        return {
            "id": f"auth-error-{hash(error_msg) % 1000000}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": model_name,
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": auth_help_message,
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": 0,
                "completion_tokens": len(auth_help_message.split()),
                "total_tokens": len(auth_help_message.split())
            }
        }

    def _create_input_error_response(self, error_msg: str, model_name: str, messages: List[Dict]) -> Dict[str, Any]:
        """
        Create a helpful error response for input-related issues
        """
        import time

        input_help_message = f"""🔧 Vertex AI Input Error

Error: {error_msg}

This error typically occurs due to input formatting issues. Here's what happened:

📋 Debugging Information:
   - Original messages: {len(messages)} message(s)
   - Model: {model_name}
   - Error type: Input validation failure

🛠️ Possible Solutions:
   1. Check if the input message is not empty
   2. Ensure proper text encoding (UTF-8)
   3. Verify message format is correct
   4. Try with a simpler message first

💡 The system has attempted to clean and validate the input, but Vertex AI still rejected it.
   This might be due to:
   - Special characters or encoding issues
   - Message length constraints
   - Content policy violations
   - Model-specific input requirements

🔄 Retry Suggestions:
   - Try with a simple message like "Hello"
   - Check for any special characters in your input
   - Ensure your message is in a supported language
"""

        return {
            "id": f"input-error-{hash(error_msg) % 1000000}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": model_name,
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": input_help_message,
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": 0,
                "completion_tokens": len(input_help_message.split()),
                "total_tokens": len(input_help_message.split())
            }
        }

    def _create_format_error_response(self, error_msg: str, model_name: str, messages: List[Dict]) -> Dict[str, Any]:
        """
        Create a helpful error response for format-related issues
        """
        import time

        format_help_message = f"""🔧 Vertex AI Format Error

Error: {error_msg}

This error indicates a problem with the request format or parameters.

📋 Common Causes:
   - Invalid parameter values
   - Unsupported model configuration
   - Malformed tool definitions
   - Incorrect message structure

🛠️ Solutions:
   1. Check parameter values (temperature, max_tokens, etc.)
   2. Verify tool definitions are properly formatted
   3. Ensure message roles are valid ('user', 'assistant', 'system')
   4. Try with default parameters first

💡 Debug Information:
   - Messages: {len(messages)}
   - Model: {model_name}
   - Error: {error_msg[:100]}...

🔄 Next Steps:
   - Simplify your request parameters
   - Remove any custom tools temporarily
   - Try with a basic message first
"""

        return {
            "id": f"format-error-{hash(error_msg) % 1000000}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": model_name,
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": format_help_message,
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": 0,
                "completion_tokens": len(format_help_message.split()),
                "total_tokens": len(format_help_message.split())
            }
        }

class VertexAIStreamAdapter:
    """
    Adapter for streaming responses from Vertex AI
    """

    def __init__(self, adapter: VertexAIAdapter):
        self.adapter = adapter

    async def stream_chat_completion(self, model_name: str, messages: List[Dict], **kwargs):
        """
        Simulate streaming by chunking the response
        """
        # For now, we'll simulate streaming by getting the full response and chunking it
        full_response = await self.adapter.async_chat_completion(model_name, messages, **kwargs)

        # Extract the content
        content = full_response["choices"][0]["message"]["content"]

        # Yield chunks
        chunk_size = 10  # Characters per chunk
        for i in range(0, len(content), chunk_size):
            chunk_content = content[i:i + chunk_size]

            chunk = {
                "id": full_response["id"],
                "object": "chat.completion.chunk",
                "created": full_response["created"],
                "model": model_name,
                "choices": [{
                    "index": 0,
                    "delta": {
                        "content": chunk_content
                    },
                    "finish_reason": None
                }]
            }

            yield chunk
            await asyncio.sleep(0.05)  # Small delay to simulate streaming

        # Final chunk
        final_chunk = {
            "id": full_response["id"],
            "object": "chat.completion.chunk",
            "created": full_response["created"],
            "model": model_name,
            "choices": [{
                "index": 0,
                "delta": {},
                "finish_reason": "stop"
            }]
        }

        yield final_chunk

def create_vertex_ai_adapter(project_id: str, location: str = "us-central1") -> Optional[VertexAIAdapter]:
    """
    Factory function to create a Vertex AI adapter
    """
    try:
        return VertexAIAdapter(project_id, location)
    except Exception as e:
        logger.error(f"❌ Failed to create Vertex AI adapter: {e}")
        return None
